package com.lx.pl.dto.mq;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

/**
 * MJ图片处理消息体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class MjImageProcessVo {

    /**
     * 任务ID (jobId)
     */
    private String jobId;

    /**
     * 用户登录名
     */
    private String loginName;

    /**
     * markId
     */
    private String markId;

    /**
     * 图片处理信息列表
     */
    private List<ImageInfo> imageInfos;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ImageInfo {
        /**
         * 原始图片URL
         */
        private String originalUrl;

        /**
         * 图片文件名
         */
        private String fileName;

        /**
         * 图片宽度
         */
        private Integer width;

        /**
         * 图片高度
         */
        private Integer height;
    }
}
