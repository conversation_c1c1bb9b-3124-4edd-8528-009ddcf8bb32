package com.lx.pl.dto.mq;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * MJ图片处理结果消息体
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@JsonIgnoreProperties(ignoreUnknown = true)
public class MjImageProcessResultVo {

    /**
     * 任务ID (jobId)
     */
    private String jobId;

    /**
     * 用户登录名
     */
    private String loginName;

    /**
     * markId
     */
    private String markId;

    /**
     * 处理状态 (SUCCESS/FAILED)
     */
    private String status;

    /**
     * 错误信息 (如果处理失败)
     */
    private String errorMessage;

    /**
     * 处理结果列表
     */
    private List<ProcessedImageInfo> processedImages;

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ProcessedImageInfo {

        @Schema(description = "原始图片生成后腾讯云地址")
        private String img_url;

        @Schema(description = "图片大小")
        private Long size;

        @Schema(description = "缩略图生成后腾讯云地址")
        private String thumbnail_url;

        @Schema(description = "高清缩略图生成后腾讯云地址")
        private String high_thumbnail_url;

        @Schema(description = "mini图")
        private String miniThumbnailUrl;

        @Schema(description = "30% 高清图")
        private String highMiniUrl;

        @Schema(description = "敏感信息")
        private String sensitive;

        @Schema(description = "原始图片真实宽")
        private int width;

        @Schema(description = "原始图片高")
        private int height;

        /**
         * 处理状态 (SUCCESS/FAILED)
         */
        private String status;

    }
}
