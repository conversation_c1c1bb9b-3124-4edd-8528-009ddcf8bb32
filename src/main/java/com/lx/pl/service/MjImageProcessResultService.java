package com.lx.pl.service;

import com.alibaba.csp.sentinel.util.StringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.lx.pl.db.mysql.gen.entity.PromptFile;
import com.lx.pl.db.mysql.gen.mapper.PromptFileMapper;
import com.lx.pl.dto.mq.MjImageProcessResultVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;

/**
 * MJ图片处理结果服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class MjImageProcessResultService {

    @Autowired
    private PromptFileMapper promptFileMapper;

    /**
     * 处理图片处理结果
     */
    public void handleImageProcessResult(MjImageProcessResultVo resultVo) {
        try {
            log.info("开始处理MJ图片处理结果，jobId: {}, status: {}", resultVo.getJobId(), resultVo.getStatus());

            if ("SUCCESS".equals(resultVo.getStatus()) && !CollectionUtils.isEmpty(resultVo.getProcessedImages())) {
                // 处理成功，更新PromptFile表中的压缩图片URL
                for (MjImageProcessResultVo.ProcessedImageInfo processedImage : resultVo.getProcessedImages()) {
                    updatePromptFileWithProcessedImages(resultVo.getJobId(), processedImage);
                }
                log.info("成功更新{}张图片的压缩信息，jobId: {}", resultVo.getProcessedImages().size(), resultVo.getJobId());
            } else {
                // 处理失败，记录错误日志
                log.error("图片处理失败，jobId: {}, errorMessage: {}", resultVo.getJobId(), resultVo.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("处理MJ图片处理结果失败，jobId: {}", resultVo.getJobId(), e);
        }
    }

    /**
     * 更新PromptFile的压缩图片信息
     */
    private void updatePromptFileWithProcessedImages(String jobId, MjImageProcessResultVo.ProcessedImageInfo processedImage) {
        try {
            if ("SUCCESS".equals(processedImage.getProcessStatus())) {
                // 通过jobId和原始图片URL查找PromptFile记录
                LambdaQueryWrapper<PromptFile> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(PromptFile::getPromptId, jobId)
                           .eq(PromptFile::getFileUrl, processedImage.getOriginalUrl());
                
                PromptFile promptFile = promptFileMapper.selectOne(queryWrapper);
                if (promptFile == null) {
                    log.warn("未找到对应的PromptFile记录，jobId: {}, originalUrl: {}", jobId, processedImage.getOriginalUrl());
                    return;
                }

                // 更新压缩图片URL
                LambdaUpdateWrapper<PromptFile> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(PromptFile::getId, promptFile.getId());

                if (StringUtil.isNotBlank(processedImage.getThumbnailUrl())) {
                    updateWrapper.set(PromptFile::getThumbnailUrl, processedImage.getThumbnailUrl());
                }
                if (StringUtil.isNotBlank(processedImage.getHighThumbnailUrl())) {
                    updateWrapper.set(PromptFile::getHighThumbnailUrl, processedImage.getHighThumbnailUrl());
                }
                if (StringUtil.isNotBlank(processedImage.getMiniThumbnailUrl())) {
                    updateWrapper.set(PromptFile::getMiniThumbnailUrl, processedImage.getMiniThumbnailUrl());
                }
                if (StringUtil.isNotBlank(processedImage.getHighMiniUrl())) {
                    updateWrapper.set(PromptFile::getHighMiniUrl, processedImage.getHighMiniUrl());
                }

                updateWrapper.set(PromptFile::getUpdateTime, LocalDateTime.now());

                int updatedRows = promptFileMapper.update(null, updateWrapper);
                if (updatedRows > 0) {
                    log.info("成功更新PromptFile压缩图片信息，promptFileId: {}, jobId: {}", promptFile.getId(), jobId);
                } else {
                    log.warn("更新PromptFile失败，promptFileId: {}, jobId: {}", promptFile.getId(), jobId);
                }
            } else {
                log.error("图片处理失败，jobId: {}, originalUrl: {}, errorMessage: {}", 
                         jobId, processedImage.getOriginalUrl(), processedImage.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("更新PromptFile压缩图片信息失败，jobId: {}, originalUrl: {}", 
                     jobId, processedImage.getOriginalUrl(), e);
        }
    }
}
